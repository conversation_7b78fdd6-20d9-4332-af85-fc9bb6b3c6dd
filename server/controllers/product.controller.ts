import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import productService from "../services/product.service";

class ProductController {
  async createProduct(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await productService.createProduct(body, user.id);
      
      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create product controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getProducts(c: Context) {
    try {
      const query = c.req.query();
      const result = await productService.getProducts(query);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get products controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getProductById(c: Context) {
    try {
      const id = c.req.param('id');
      const result = await productService.getProductById(id);

      if (!result.status) {
        return c.json(result, 404);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get product controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async updateProduct(c: Context) {
    try {
      const id = c.req.param('id');
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await productService.updateProduct(id, body, user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Update product controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async deleteProduct(c: Context) {
    try {
      const id = c.req.param('id');
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await productService.deleteProduct(id, user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Delete product controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getCategories(c: Context) {
    try {
      const result = await productService.getCategories();

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get categories controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async createCategory(c: Context) {
    try {
      const body = await c.req.json();
      const result = await productService.createCategory(body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create category controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async getItemTypes(c: Context) {
    try {
      const categoryId = c.req.query('categoryId');
      const result = await productService.getItemTypes(categoryId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 200);
    } catch (error) {
      console.error("Get item types controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async createItemType(c: Context) {
    try {
      const body = await c.req.json();
      const result = await productService.createItemType(body);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Create item type controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async placeBid(c: Context) {
    try {
      const body = await c.req.json();
      const user = c.get('user');

      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await productService.placeBid(body, user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result, 201);
    } catch (error) {
      console.error("Place bid controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }

  async uploadImages(c: Context) {
    try {
      // This will be implemented when we add file upload middleware
      // For now, return a placeholder response
      return c.json({
        status: true,
        message: "Image upload endpoint - to be implemented",
        data: {
          urls: [
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg"
          ]
        }
      }, 200);
    } catch (error) {
      console.error("Upload images controller error:", error);
      return c.json(
        errorResponse(`${(error as Error).message || "An error occurred"}`),
        500
      );
    }
  }
}

export default new ProductController();
