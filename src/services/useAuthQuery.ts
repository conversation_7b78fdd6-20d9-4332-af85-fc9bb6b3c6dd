import { useMutation, useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import axios, { AxiosResponse } from "axios";
import { toaster } from "@/components/ui/toaster";

// Types
interface AuthResponse {
  status: boolean;
  message: string;
  data?: any;
}

interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
}

interface LoginData {
  emailPhoneNumber: string;
  password: string;
}

// API Base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1';

// Axios instance with interceptors
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    // Token will be added by NextAuth session
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response.data,
  (error) => {
    const message = error.response?.data?.message || error.message || 'An error occurred';
    return Promise.reject(new Error(message));
  }
);

// Custom hook to get authenticated API client
export const useAuthenticatedApi = () => {
  const { data: session } = useSession();

  const authenticatedClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
      ...(session?.accessToken && {
        Authorization: `Bearer ${session.accessToken}`
      })
    }
  });

  authenticatedClient.interceptors.response.use(
    (response: AxiosResponse) => response.data,
    (error) => {
      const message = error.response?.data?.message || error.message || 'An error occurred';
      return Promise.reject(new Error(message));
    }
  );

  return authenticatedClient;
};

// Auth Mutations
export const useRegisterMutation = () => {
  return useMutation<AuthResponse, Error, RegisterData>({
    mutationFn: async (data: RegisterData) => {
      return await apiClient.post('/auth/register', data);
    },
    onSuccess: (data) => {
      toaster.create({
        title: "Success",
        description: data.message || "Registration successful",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Registration Failed",
        description: error.message,
        type: "error",
      });
    },
  });
};

export const useLoginMutation = () => {
  return useMutation<AuthResponse, Error, LoginData>({
    mutationFn: async (data: LoginData) => {
      return await apiClient.post('/auth/login', data);
    },
    onSuccess: (data) => {
      toaster.create({
        title: "Success",
        description: data.message || "Login successful",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Login Failed",
        description: error.message,
        type: "error",
      });
    },
  });
};

// Profile Query
export const useProfileQuery = () => {
  const { data: session, status } = useSession();
  const authenticatedClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['profile', session?.user?.id],
    queryFn: async () => {
      return await authenticatedClient.get('/auth/profile');
    },
    enabled: status === 'authenticated' && !!session?.accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
};
