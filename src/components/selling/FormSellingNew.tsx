"use client"
import { Box, <PERSON>ing, HStack, Icon, RadioCard, Stack, Text, Button } from '@chakra-ui/react'
import { Checkbox } from '@chakra-ui/react'
import Link from 'next/link'
import React, { useEffect } from 'react'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FaArrowLeft } from 'react-icons/fa'
import FormInputField from '../ui/form/FormInputField'
import FormSelectField, { SelectOption } from '../ui/form/FormSelectField'
import FormImageUpload from '../ui/form/FormImageUpload'
import FormTextArea from '../ui/form/FormTextArea'
import FormDateTimePicker from '../ui/form/FormDateTimePicker'
import { SingleValue } from 'react-select'
import { 
  useCategoriesQuery, 
  useItemTypesQuery, 
  useCreateProductMutation,
  CreateProductData 
} from '@/services/useProductQuery'

interface FormStepProps {}

type TypeSell = {
    value: string;
    title: string;
}

const TypeSell: TypeSell[] = [
    {
        value: 'auction',
        title: 'Auction',
    },
    {
        value: 'buy-now',
        title: 'Buy Now',
    },
];

// Form validation schema
const formSchema = z.object({
    sellType: z.enum(['auction', 'buy-now']),
    itemName: z.string().min(1, 'Item name is required').max(255),
    categoryId: z.string().min(1, 'Category is required'),
    itemTypeId: z.string().min(1, 'Item type is required'),
    images: z.array(z.any()).min(1, 'At least one image is required'),
    priceUSD: z.number().positive('Price must be positive'),
    description: z.string().optional(),
    auctionStartDate: z.string().optional(),
    auctionEndDate: z.string().optional(),
    extendedBiddingEnabled: z.boolean().default(false),
    extendedBiddingMinutes: z.number().int().min(1).max(60).optional(),
    extendedBiddingDuration: z.number().int().min(1).max(120).optional(),
}).refine((data) => {
    if (data.sellType === 'auction') {
        return data.auctionStartDate && data.auctionEndDate;
    }
    return true;
}, {
    message: "Auction start and end dates are required for auction type",
    path: ["auctionStartDate"]
}).refine((data) => {
    if (data.extendedBiddingEnabled) {
        return data.extendedBiddingMinutes && data.extendedBiddingDuration;
    }
    return true;
}, {
    message: "Extended bidding settings are required when enabled",
    path: ["extendedBiddingMinutes"]
});

type FormData = z.infer<typeof formSchema>;

const FormSellingNew: React.FC<FormStepProps> = () => {
    // React Hook Form setup
    const {
        control,
        handleSubmit,
        watch,
        setValue,
        formState: { errors, isSubmitting }
    } = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            sellType: 'auction',
            itemName: '',
            categoryId: '',
            itemTypeId: '',
            images: [],
            priceUSD: 0,
            description: '',
            auctionStartDate: '',
            auctionEndDate: '',
            extendedBiddingEnabled: false,
            extendedBiddingMinutes: 5,
            extendedBiddingDuration: 10
        }
    });

    // Watch form values
    const watchedSellType = watch('sellType');
    const watchedCategoryId = watch('categoryId');
    const watchedExtendedBidding = watch('extendedBiddingEnabled');

    // React Query hooks
    const { data: categories = [] } = useCategoriesQuery();
    const { data: itemTypes = [] } = useItemTypesQuery(watchedCategoryId);
    const createProductMutation = useCreateProductMutation();

    // Reset item type when category changes
    useEffect(() => {
        if (watchedCategoryId) {
            setValue('itemTypeId', '');
        }
    }, [watchedCategoryId, setValue]);

    // Convert categories to select options
    const categoryOptions: SelectOption[] = categories.map(category => ({
        value: category.id,
        label: category.name
    }));

    // Convert item types to select options
    const itemTypeOptions: SelectOption[] = itemTypes.map(itemType => ({
        value: itemType.id,
        label: itemType.name
    }));

    // Form submit handler
    const onSubmit = async (data: FormData) => {
        try {
            const submitData: CreateProductData = {
                itemName: data.itemName,
                description: data.description,
                sellType: data.sellType,
                priceUSD: data.priceUSD,
                categoryId: data.categoryId,
                itemTypeId: data.itemTypeId,
                auctionStartDate: data.auctionStartDate,
                auctionEndDate: data.auctionEndDate,
                extendedBiddingEnabled: data.extendedBiddingEnabled,
                extendedBiddingMinutes: data.extendedBiddingMinutes,
                extendedBiddingDuration: data.extendedBiddingDuration,
                images: data.images.map((img: any, index: number) => ({
                    imageUrl: img.preview,
                    altText: `Product image ${index + 1}`,
                    sortOrder: index,
                    isMain: img.isMain || index === 0
                }))
            };

            await createProductMutation.mutateAsync(submitData);
            
            // Reset form on success
            setValue('sellType', 'auction');
            setValue('itemName', '');
            setValue('categoryId', '');
            setValue('itemTypeId', '');
            setValue('images', []);
            setValue('priceUSD', 0);
            setValue('description', '');
            setValue('auctionStartDate', '');
            setValue('auctionEndDate', '');
            setValue('extendedBiddingEnabled', false);
            setValue('extendedBiddingMinutes', 5);
            setValue('extendedBiddingDuration', 10);
            
        } catch (error) {
            console.error('Error submitting form:', error);
        }
    };

    return (
        <Box mx={"auto"} w="full">
            <HStack alignItems="center" gap={4}>
                <Link href="/">
                    <Icon as={FaArrowLeft} boxSize={6} color="gray.600" />
                </Link>
                <Heading as="h1" size="lg" fontWeight="bold">
                    Sell Your Item
                </Heading>
            </HStack>

            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack gap={6} bg="white" p={8} mt={6} borderRadius={8} boxShadow="xs">
                    <Heading as="h3" size="md" mb={6} fontWeight="bold">
                        Information Product
                    </Heading>
                    
                    {/* Type Selling */}
                    <Controller
                        name="sellType"
                        control={control}
                        render={({ field }) => (
                            <RadioCard.Root value={field.value} onValueChange={field.onChange}>
                                <RadioCard.Label fontWeight="bold" color={"gray.800"}>
                                    Type Selling  <Box as="span" color="red.500">*</Box>
                                </RadioCard.Label>
                                <RadioCard.Label mb={2} color={"gray.500"}>
                                    Please select the type of selling you want to create.
                                </RadioCard.Label>
                                <HStack align="stretch">
                                    {TypeSell.map((item) => (
                                        <RadioCard.Item key={item.value} value={item.value}>
                                            <RadioCard.ItemHiddenInput />
                                            <RadioCard.ItemControl>
                                                <RadioCard.ItemText>{item.title}</RadioCard.ItemText>
                                                <RadioCard.ItemIndicator />
                                            </RadioCard.ItemControl>
                                        </RadioCard.Item>
                                    ))}
                                </HStack>
                                {errors.sellType && (
                                    <Text color="red.500" fontSize="sm">{errors.sellType.message}</Text>
                                )}
                            </RadioCard.Root>
                        )}
                    />

                    {/* Item Name */}
                    <Controller
                        name="itemName"
                        control={control}
                        render={({ field }) => (
                            <FormInputField
                                label="Item Name"
                                description='Enter the name of the item you want to sell.'
                                placeholder="Example: Vintage Watch"
                                required
                                value={field.value}
                                onChange={field.onChange}
                                errorText={errors.itemName?.message}
                            />
                        )}
                    />

                    {/* Category */}
                    <Controller
                        name="categoryId"
                        control={control}
                        render={({ field }) => (
                            <FormSelectField
                                label="Category"
                                required
                                placeholder="Select Category"
                                options={categoryOptions}
                                width="100%"
                                value={categoryOptions.find(opt => opt.value === field.value) || null}
                                onChange={(selectedOption) => {
                                    const option = selectedOption as SingleValue<SelectOption>;
                                    field.onChange(option?.value || '');
                                }}
                                errorText={errors.categoryId?.message}
                            />
                        )}
                    />

                    {/* Item Type */}
                    <Controller
                        name="itemTypeId"
                        control={control}
                        render={({ field }) => (
                            <FormSelectField
                                label="Item Type"
                                required
                                placeholder="Select Item Type"
                                options={itemTypeOptions}
                                width="100%"
                                value={itemTypeOptions.find(opt => opt.value === field.value) || null}
                                onChange={(selectedOption) => {
                                    const option = selectedOption as SingleValue<SelectOption>;
                                    field.onChange(option?.value || '');
                                }}
                                errorText={errors.itemTypeId?.message}
                            />
                        )}
                    />

                    {/* Product Images */}
                    <Controller
                        name="images"
                        control={control}
                        render={({ field }) => (
                            <FormImageUpload
                                label="Product Images"
                                description="Upload multiple images of your product. First image will be the main image."
                                required
                                maxFiles={10}
                                value={field.value}
                                onImagesChange={field.onChange}
                                errorText={errors.images?.message}
                            />
                        )}
                    />

                    {/* Price USD */}
                    <Controller
                        name="priceUSD"
                        control={control}
                        render={({ field }) => (
                            <FormInputField
                                label="Price (USD)"
                                description='Enter the price in US Dollars.'
                                placeholder="100.00"
                                type="number"
                                required
                                value={field.value.toString()}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                errorText={errors.priceUSD?.message}
                            />
                        )}
                    />

                    {/* Description */}
                    <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                            <FormTextArea
                                label="Product Description"
                                description="Provide a detailed description of your product."
                                placeholder="Describe your product in detail..."
                                rows={6}
                                maxLength={2000}
                                value={field.value || ''}
                                onChange={field.onChange}
                                errorText={errors.description?.message}
                            />
                        )}
                    />

                    {/* Submit Button */}
                    <Button
                        type="submit"
                        colorScheme="blue"
                        size="lg"
                        loading={isSubmitting || createProductMutation.isPending}
                        disabled={isSubmitting || createProductMutation.isPending}
                        mt={6}
                    >
                        {isSubmitting || createProductMutation.isPending ? 'Creating...' : 'Create Listing'}
                    </Button>
                </Stack>
            </form>
        </Box>
    );
};

export default FormSellingNew;
