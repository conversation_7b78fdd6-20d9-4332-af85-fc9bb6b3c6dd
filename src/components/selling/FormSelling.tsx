"use client"
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ck, Icon, <PERSON>Card, <PERSON>ack, Text, Button } from '@chakra-ui/react'
import { Checkbox } from '@chakra-ui/react'
import Link from 'next/link'
import React, { useState, useEffect } from 'react'
import { FaArrowLeft } from 'react-icons/fa'
import FormInputField from '../ui/form/FormInputField'
import FormSelect<PERSON>ield, { SelectOption } from '../ui/form/FormSelectField'
import FormImageUpload from '../ui/form/FormImageUpload'
import FormTextArea from '../ui/form/FormTextArea'
import FormDateTimePicker from '../ui/form/FormDateTimePicker'
import { SingleValue } from 'react-select'
import { useSession } from 'next-auth/react'

interface FormStepProps {

}

type TypeSell = {
    value: string;
    title: string;
}

const TypeSell: TypeSell[] = [
    {
        value: 'auction',
        title: 'Auction',
    },
    {
        value: 'buy-now',
        title: 'Buy Now',
    },
];

interface FormData {
    sellType: string;
    itemName: string;
    category: string;
    itemType: string;
    images: any[];
    priceUSD: string;
    description: string;
    auctionStartDate: string;
    auctionEndDate: string;
    extendedBiddingEnabled: boolean;
    extendedBiddingMinutes: string;
    extendedBiddingDuration: string;
}

const FormSelling: React.FC<FormStepProps> = () => {
    const session = useSession();
    const [formData, setFormData] = useState<FormData>({
        sellType: 'auction',
        itemName: '',
        category: '',
        itemType: '',
        images: [],
        priceUSD: '',
        description: '',
        auctionStartDate: '',
        auctionEndDate: '',
        extendedBiddingEnabled: false,
        extendedBiddingMinutes: '5',
        extendedBiddingDuration: '10'
    });

    const [categoryOptions, setCategoryOptions] = useState<SelectOption[]>([]);
    const [itemTypeOptions, setItemTypeOptions] = useState<SelectOption[]>([]);
    const [loading, setLoading] = useState(false);

    // Fetch categories on component mount
    useEffect(() => {
        fetchCategories();
    }, []);

    // Fetch item types when category changes
    useEffect(() => {
        if (formData.category) {
            fetchItemTypes(formData.category);
        } else {
            setItemTypeOptions([]);
        }
    }, [formData.category]);

    const fetchCategories = async () => {
        try {
            const response = await fetch('/api/v1/master/categories');
            const result = await response.json();

            if (result.status && result.data) {
                const options = result.data.map((category: any) => ({
                    value: category.id,
                    label: category.name
                }));
                setCategoryOptions(options);
            }
        } catch (error) {
            console.error('Error fetching categories:', error);
        }
    };

    const fetchItemTypes = async (categoryId: string) => {
        try {
            const response = await fetch(`/api/v1/master/item-types?categoryId=${categoryId}`);
            const result = await response.json();

            if (result.status && result.data) {
                const options = result.data.map((itemType: any) => ({
                    value: itemType.id,
                    label: itemType.name
                }));
                setItemTypeOptions(options);
            }
        } catch (error) {
            console.error('Error fetching item types:', error);
        }
    };

    const handleInputChange = (field: keyof FormData, value: any) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSubmit = async () => {
        setLoading(true);
        try {
            // Prepare form data for submission
            const submitData = {
                itemName: formData.itemName,
                description: formData.description,
                sellType: formData.sellType,
                priceUSD: parseFloat(formData.priceUSD),
                categoryId: formData.category,
                itemTypeId: formData.itemType,
                auctionStartDate: formData.auctionStartDate || undefined,
                auctionEndDate: formData.auctionEndDate || undefined,
                extendedBiddingEnabled: formData.extendedBiddingEnabled,
                extendedBiddingMinutes: formData.extendedBiddingEnabled ? parseInt(formData.extendedBiddingMinutes) : undefined,
                extendedBiddingDuration: formData.extendedBiddingEnabled ? parseInt(formData.extendedBiddingDuration) : undefined,
                images: formData.images.map((img: any, index: number) => ({
                    imageUrl: img.preview, // In real implementation, this would be uploaded URL
                    altText: `Product image ${index + 1}`,
                    sortOrder: index,
                    isMain: img.isMain || index === 0
                }))
            };

            const response = await fetch('/api/v1/products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    "Authorization": `Bearer eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..K6nsXa3Dswg80b8i.4lxXQPPdLqGihZKa4hVm0AU6Ts5FYGo2ti_BLiWuIIGFmhusCs1_CmQPSoSUKgK607p1iwbK-qjxwF54RziBn3k-ExpdLWEmqa2SdbhcLAjOFB0kyeJ2H_JoXQLz_2fAGvkWFuJ2EQZRKfpb5G5418-dQ_HFGbIw3PZxp2bhoDW36wA0zuu4iqBApHMATSqvNFW6fRWgH067xmjS17-vLGAMsALiWGKEfkb-5CrQ1Ln8f9BlN130A58huljSwqtWKCmU02boxbx1J2hXh-EE3A_-qywhlUl8L_--GWgjIH2j4S6RAWNGn1F_6znpkVifRiMVg15TeiRH9cG1Qp7DXFwcJejaiswEniVxHFzGffbR.iuA548J_UvRL1VJ5ab8gsg` 
                    // Add authorization header when auth is implemented
                },
                body: JSON.stringify(submitData)
            });

            const result = await response.json();

            if (result.status) {
                alert('Product created successfully!');
                // Reset form or redirect
                setFormData({
                    sellType: 'auction',
                    itemName: '',
                    category: '',
                    itemType: '',
                    images: [],
                    priceUSD: '',
                    description: '',
                    auctionStartDate: '',
                    auctionEndDate: '',
                    extendedBiddingEnabled: false,
                    extendedBiddingMinutes: '5',
                    extendedBiddingDuration: '10'
                });
            } else {
                alert('Error creating product: ' + result.message);
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            alert('Error creating product. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Box mx={"auto"} w="full">
            <HStack alignItems="center" gap={4}>
                <Link href="/">
                    <Icon as={FaArrowLeft} boxSize={4} />
                </Link>
                <Box>
                    <Heading as="div" fontWeight="bold" size="lg" color="gray.800">
                        Selling Items
                    </Heading>
                    <Text as="div" fontSize="sm" color="gray.500">
                        Create a new auction listing to sell your items.
                    </Text>
                </Box>
            </HStack>
            <Stack gap={6} bg="white" p={8} mt={6} borderRadius={8} boxShadow="xs">
                <Heading as="h3" size="md" mb={6} fontWeight="bold">
                    Information Product
                </Heading>

                {/* Type Selling */}
                <RadioCard.Root
                    value={formData.sellType}
                    onValueChange={(value) => handleInputChange('sellType', value)}
                >
                    <RadioCard.Label fontWeight="bold" color={"gray.800"}>
                        Type Selling  <Box as="span" color="red.500">*</Box>
                    </RadioCard.Label>
                    <RadioCard.Label mb={2} color={"gray.500"}>
                        Please select the type of selling you want to create.
                    </RadioCard.Label>
                    <HStack align="stretch">
                        {TypeSell.map((item) => (
                            <RadioCard.Item key={item.value} value={item.value}>
                                <RadioCard.ItemHiddenInput />
                                <RadioCard.ItemControl>
                                    <RadioCard.ItemText>{item.title}</RadioCard.ItemText>
                                    <RadioCard.ItemIndicator />
                                </RadioCard.ItemControl>
                            </RadioCard.Item>
                        ))}
                    </HStack>
                </RadioCard.Root>

                {/* Item Name */}
                <FormInputField
                    label="Item Name"
                    description='Enter the name of the item you want to sell.'
                    placeholder="Example: Vintage Watch"
                    required
                    value={formData.itemName}
                    onChange={(e) => handleInputChange('itemName', e.target.value)}
                />

                {/* Category */}
                <FormSelectField
                    label="Category"
                    required
                    placeholder="Select Category"
                    options={categoryOptions}
                    width="100%"
                    onChange={(selectedOption) => {
                        const option = selectedOption as SingleValue<SelectOption>;
                        handleInputChange('category', option?.value || '');
                    }}
                />

                {/* Item Type */}
                <FormSelectField
                    label="Item Type"
                    required
                    placeholder="Select Item Type"
                    options={itemTypeOptions}
                    width="100%"
                    onChange={(selectedOption) => {
                        const option = selectedOption as SingleValue<SelectOption>;
                        handleInputChange('itemType', option?.value || '');
                    }}
                />

                {/* Product Images */}
                <FormImageUpload
                    label="Product Images"
                    description="Upload multiple images of your product. First image will be the main image."
                    required
                    maxFiles={10}
                    onImagesChange={(images) => handleInputChange('images', images)}
                />

                {/* Price USD */}
                <FormInputField
                    label="Price (USD)"
                    description='Enter the price in US Dollars.'
                    placeholder="100.00"
                    type="number"
                    required
                    value={formData.priceUSD}
                    onChange={(e) => handleInputChange('priceUSD', e.target.value)}
                />

                {/* Description */}
                <FormTextArea
                    label="Product Description"
                    description="Provide a detailed description of your product."
                    placeholder="Describe your product in detail..."
                    required
                    rows={6}
                    maxLength={2000}
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                />

                {/* Auction Settings - Only show if auction type */}
                {formData.sellType === 'auction' && (
                    <Stack gap={4}>
                        <Heading as="h4" size="sm" fontWeight="bold" color="gray.800">
                            Auction Settings
                        </Heading>

                        {/* Auction Start Date */}
                        <FormDateTimePicker
                            label="Auction Start Date"
                            description="When should the auction start?"
                            required
                            value={formData.auctionStartDate}
                            onChange={(value) => handleInputChange('auctionStartDate', value)}
                            min={new Date().toISOString().slice(0, 16)}
                        />

                        {/* Auction End Date */}
                        <FormDateTimePicker
                            label="Auction End Date"
                            description="When should the auction end?"
                            required
                            value={formData.auctionEndDate}
                            onChange={(value) => handleInputChange('auctionEndDate', value)}
                            min={formData.auctionStartDate || new Date().toISOString().slice(0, 16)}
                        />

                        {/* Extended Bidding Settings */}
                        <Box>
                            <Checkbox.Root
                                checked={formData.extendedBiddingEnabled}
                                onCheckedChange={(checked) => handleInputChange('extendedBiddingEnabled', checked)}
                            >
                                <Checkbox.HiddenInput />
                                <Checkbox.Control />
                                <Checkbox.Label>
                                    <Text fontWeight="bold" color="gray.800">
                                        Enable Extended Bidding
                                    </Text>
                                </Checkbox.Label>
                            </Checkbox.Root>
                            <Text fontSize="sm" color="gray.500" mt={1}>
                                Automatically extend auction when bids are placed near the end
                            </Text>
                        </Box>

                        {formData.extendedBiddingEnabled && (
                            <HStack gap={4}>
                                <FormInputField
                                    label="Trigger Minutes"
                                    description="Minutes before end to trigger extension"
                                    placeholder="5"
                                    type="number"
                                    value={formData.extendedBiddingMinutes}
                                    onChange={(e) => handleInputChange('extendedBiddingMinutes', e.target.value)}
                                />
                                <FormInputField
                                    label="Extension Duration"
                                    description="How long to extend (minutes)"
                                    placeholder="10"
                                    type="number"
                                    value={formData.extendedBiddingDuration}
                                    onChange={(e) => handleInputChange('extendedBiddingDuration', e.target.value)}
                                />
                            </HStack>
                        )}
                    </Stack>
                )}

                {/* Submit Button */}
                <Button
                    colorScheme="blue"
                    size="lg"
                    onClick={handleSubmit}
                    loading={loading}
                    disabled={loading}
                    mt={6}
                >
                    {loading ? 'Creating...' : 'Create Listing'}
                </Button>
            </Stack>

        </Box>
    )
}

export default FormSelling