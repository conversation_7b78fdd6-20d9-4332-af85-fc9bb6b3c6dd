"use client"
import React, { useState, useEffect } from 'react'
import {
    Box,
    Button,
    Text,
    VStack,
    HStack,
    Badge,
    Input,
    Stack,
    Heading,
    Flex,
    Progress,
    Stat,
    StatLabel,
    // StatNumber,
    StatHelpText
} from '@chakra-ui/react'
import { FaGavel, FaHeart, FaClock, FaHistory } from 'react-icons/fa'
import { formatUSD } from '@/utils/helpers/helper'
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns'
import { usePlaceBidMutation, useUserBidQuery, useBidHistoryQuery } from '@/services/useBiddingQuery'

interface AuctionInfoProps {
    currentBid: number;
    startingPrice: number;
    bidCount: number;
    auctionStartDate: string;
    auctionEndDate: string;
    extendedBiddingEnabled?: boolean;
    extendedBiddingMinutes?: number;
    extendedBiddingDuration?: number;
    productId: string;
    productName: string;
    onPlaceBid?: (productId: string, bidAmount: number) => void;
    onAddToWishlist?: (productId: string) => void;
    isInWishlist?: boolean;
    onShowBidHistory?: (productId: string) => void;
    mb?: number;
}

const AuctionInfo: React.FC<AuctionInfoProps> = ({
    currentBid,
    startingPrice,
    bidCount,
    auctionStartDate,
    auctionEndDate,
    extendedBiddingEnabled = false,
    extendedBiddingMinutes = 5,
    extendedBiddingDuration = 10,
    productId,
    productName,
    onPlaceBid,
    onAddToWishlist,
    isInWishlist = false,
    onShowBidHistory,
    mb = 0
}) => {
    const [bidAmount, setBidAmount] = useState(currentBid + 1);
    const [timeLeft, setTimeLeft] = useState('');
    const [auctionStatus, setAuctionStatus] = useState<'upcoming' | 'active' | 'ended'>('upcoming');

    // React Query hooks
    const placeBidMutation = usePlaceBidMutation();
    const { data: userBid } = useUserBidQuery(productId);
    const { data: bidHistory } = useBidHistoryQuery(productId);

    const minBidIncrement = Math.max(1, Math.floor(currentBid * 0.05)); // 5% increment or $1 minimum
    const minBidAmount = currentBid + minBidIncrement;

    // Update auction status and time left
    useEffect(() => {
        const updateAuctionInfo = () => {
            const now = new Date();
            const startDate = new Date(auctionStartDate);
            const endDate = new Date(auctionEndDate);

            if (isBefore(now, startDate)) {
                setAuctionStatus('upcoming');
                setTimeLeft(`Starts ${formatDistanceToNow(startDate, { addSuffix: true })}`);
            } else if (isAfter(now, endDate)) {
                setAuctionStatus('ended');
                setTimeLeft('Auction ended');
            } else {
                setAuctionStatus('active');
                setTimeLeft(`Ends ${formatDistanceToNow(endDate, { addSuffix: true })}`);
            }
        };

        updateAuctionInfo();
        const interval = setInterval(updateAuctionInfo, 1000); // Update every second

        return () => clearInterval(interval);
    }, [auctionStartDate, auctionEndDate]);

    // Update bid amount when current bid changes
    useEffect(() => {
        setBidAmount(minBidAmount);
    }, [minBidAmount]);

    const handleBidAmountChange = (value: string) => {
        const numValue = parseFloat(value) || 0;
        setBidAmount(numValue);
    };

    const handlePlaceBid = async () => {
        if (bidAmount >= minBidAmount) {
            try {
                await placeBidMutation.mutateAsync({
                    productId,
                    bidAmount
                });
                // Reset bid amount to new minimum after successful bid
                setBidAmount(bidAmount + minBidIncrement);
            } catch (error) {
                console.error('Failed to place bid:', error);
            }
        }
    };

    const handleAddToWishlist = () => {
        if (onAddToWishlist) {
            onAddToWishlist(productId);
        }
    };

    const getStatusColor = () => {
        switch (auctionStatus) {
            case 'upcoming': return 'blue';
            case 'active': return 'green';
            case 'ended': return 'gray';
            default: return 'gray';
        }
    };

    const getStatusText = () => {
        switch (auctionStatus) {
            case 'upcoming': return 'Upcoming';
            case 'active': return 'Live Auction';
            case 'ended': return 'Auction Ended';
            default: return 'Unknown';
        }
    };

    return (
        <Box
            bg="white"
            p={6}
            borderRadius="lg"
            border="1px solid"
            borderColor="gray.200"
            mb={mb}
        >
            <VStack align="stretch" gap={4}>
                {/* Auction Status */}
                <Flex justify="space-between" align="center">
                    <Badge colorScheme={getStatusColor()} variant="solid" fontSize="sm" px={3} py={1}>
                        {getStatusText()}
                    </Badge>
                    <HStack>
                        <FaClock />
                        <Text fontSize="sm" color="gray.600">
                            {timeLeft}
                        </Text>
                    </HStack>
                </Flex>

                {/* Current Bid Info */}
                {/* <Box>
                    <Stat>
                        <StatLabel>Current Bid</StatLabel>
                        <StatNumber color="blue.600" fontSize="2xl">
                            {formatUSD(currentBid)}
                        </StatNumber>
                        <StatHelpText>
                            {bidCount} bid{bidCount !== 1 ? 's' : ''} • Starting at {formatUSD(startingPrice)}
                        </StatHelpText>
                    </Stat>
                </Box> */}

                {/* User's Bid Status */}
                {userBid && (
                    <Box p={3} bg={userBid.isWinning ? "green.50" : "orange.50"} borderRadius="md">
                        <Text fontSize="sm" color={userBid.isWinning ? "green.700" : "orange.700"}>
                            {userBid.isWinning ? "🎉 You're the highest bidder!" : "⚠️ You've been outbid"}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                            Your highest bid: {formatUSD(userBid.bidAmount)}
                        </Text>
                    </Box>
                )}

                {/* Extended Bidding Info */}
                {extendedBiddingEnabled && auctionStatus === 'active' && (
                    <Box p={3} bg="blue.50" borderRadius="md">
                        <Text fontSize="xs" color="blue.700">
                            🔄 Extended Bidding: Auction extends {extendedBiddingDuration} minutes if bid placed in last {extendedBiddingMinutes} minutes
                        </Text>
                    </Box>
                )}

                {/* Bid Placement */}
                {auctionStatus === 'active' && (
                    <Box>
                        <Text fontSize="sm" color="gray.600" mb={2}>
                            Place Your Bid (Minimum: {formatUSD(minBidAmount)})
                        </Text>
                        <HStack>
                            <Input
                                type="number"
                                value={bidAmount}
                                onChange={(e) => handleBidAmountChange(e.target.value)}
                                placeholder={`Min: ${formatUSD(minBidAmount)}`}
                                min={minBidAmount}
                                step={minBidIncrement}
                            />
                            <Button
                                colorScheme="blue"
                                onClick={handlePlaceBid}
                                loading={placeBidMutation.isPending}
                                disabled={placeBidMutation.isPending || bidAmount < minBidAmount}
                                minW="120px"
                            >
                                <FaGavel />
                                Place Bid
                            </Button>
                        </HStack>
                        {bidAmount < minBidAmount && (
                            <Text fontSize="xs" color="red.500" mt={1}>
                                Bid must be at least {formatUSD(minBidAmount)}
                            </Text>
                        )}
                    </Box>
                )}

                {/* Auction Ended Message */}
                {auctionStatus === 'ended' && (
                    <Box p={4} bg="gray.50" borderRadius="md" textAlign="center">
                        <Text fontWeight="bold" color="gray.700">
                            Auction has ended
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                            Final bid: {formatUSD(currentBid)}
                        </Text>
                        {userBid?.isWinning && (
                            <Text fontSize="sm" color="green.600" fontWeight="bold" mt={2}>
                                🎉 Congratulations! You won this auction!
                            </Text>
                        )}
                    </Box>
                )}

                {/* Upcoming Auction Message */}
                {auctionStatus === 'upcoming' && (
                    <Box p={4} bg="blue.50" borderRadius="md" textAlign="center">
                        <Text fontWeight="bold" color="blue.700">
                            Auction hasn't started yet
                        </Text>
                        <Text fontSize="sm" color="blue.600">
                            Starting bid: {formatUSD(startingPrice)}
                        </Text>
                    </Box>
                )}

                {/* Action Buttons */}
                <HStack gap={2}>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleAddToWishlist}
                        color={isInWishlist ? "red.500" : "gray.600"}
                        _hover={{
                            color: isInWishlist ? "red.600" : "red.500",
                            bg: isInWishlist ? "red.50" : "gray.50"
                        }}
                        flex={1}
                    >
                        <FaHeart />
                        {isInWishlist ? 'Remove from Watchlist' : 'Add to Watchlist'}
                    </Button>

                    {bidHistory && bidHistory.totalBids > 0 && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onShowBidHistory?.(productId)}
                            color="blue.600"
                            _hover={{
                                color: "blue.700",
                                bg: "blue.50"
                            }}
                        >
                            <FaHistory />
                            Bid History ({bidHistory.totalBids})
                        </Button>
                    )}
                </HStack>

                {/* Auction Info */}
                <Box pt={4} borderTop="1px solid" borderColor="gray.100">
                    <Text fontSize="xs" color="gray.500">
                        • Bid increments: {formatUSD(minBidIncrement)} minimum
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Winner pays within 48 hours
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Secure payment processing
                    </Text>
                </Box>
            </VStack>
        </Box>
    );
};

export default AuctionInfo;
