"use client"
import React, { useState } from 'react'
import {
    Box,
    Button,
    Text,
    VStack,
    HStack,
    Badge,
    Input,
    Stack,
    Heading,
    Flex,
    IconButton,
    useDisclosure
} from '@chakra-ui/react'
import { FaMinus, FaPlus, FaShoppingCart, FaHeart } from 'react-icons/fa'
import { formatUSD } from '@/utils/helpers/helper'

interface BuyNowInfoProps {
    price: number;
    stock?: number;
    productId: string;
    productName: string;
    onAddToCart?: (productId: string, quantity: number) => void;
    onBuyNow?: (productId: string, quantity: number) => void;
    onAddToWishlist?: (productId: string) => void;
    isInWishlist?: boolean;
    isInCart?: boolean;
    mb?: number;
}

const BuyNowInfo: React.FC<BuyNowInfoProps> = ({
    price,
    stock = 1,
    productId,
    productName,
    onAddToCart,
    onBuyNow,
    onAddToWishlist,
    isInWishlist = false,
    isInCart = false,
    mb = 0
}) => {
    const [quantity, setQuantity] = useState(1);
    const [isAddingToCart, setIsAddingToCart] = useState(false);
    const [isBuying, setIsBuying] = useState(false);

    const handleQuantityChange = (newQuantity: number) => {
        if (newQuantity >= 1 && newQuantity <= stock) {
            setQuantity(newQuantity);
        }
    };

    const handleAddToCart = async () => {
        if (onAddToCart) {
            setIsAddingToCart(true);
            try {
                await onAddToCart(productId, quantity);
            } finally {
                setIsAddingToCart(false);
            }
        }
    };

    const handleBuyNow = async () => {
        if (onBuyNow) {
            setIsBuying(true);
            try {
                await onBuyNow(productId, quantity);
            } finally {
                setIsBuying(false);
            }
        }
    };

    const handleAddToWishlist = () => {
        if (onAddToWishlist) {
            onAddToWishlist(productId);
        }
    };

    const totalPrice = price * quantity;

    return (
        <Box
            bg="white"
            p={6}
            borderRadius="lg"
            border="1px solid"
            borderColor="gray.200"
            mb={mb}
        >
            <VStack align="stretch" gap={4}>
                {/* Price Section */}
                <Box>
                    <Text fontSize="sm" color="gray.600" mb={1}>
                        Price
                    </Text>
                    <Heading as="h3" size="lg" color="green.600">
                        {formatUSD(price)}
                    </Heading>
                    {stock > 0 ? (
                        <Badge colorScheme="green" variant="subtle" mt={2}>
                            In Stock ({stock} available)
                        </Badge>
                    ) : (
                        <Badge colorScheme="red" variant="subtle" mt={2}>
                            Out of Stock
                        </Badge>
                    )}
                </Box>

                {/* Quantity Selector */}
                {stock > 0 && (
                    <Box>
                        <Text fontSize="sm" color="gray.600" mb={2}>
                            Quantity
                        </Text>
                        <HStack>
                            <IconButton
                                aria-label="Decrease quantity"
                                icon={<FaMinus />}
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuantityChange(quantity - 1)}
                                disabled={quantity <= 1}
                            />
                            <Input
                                value={quantity}
                                onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                                textAlign="center"
                                width="80px"
                                size="sm"
                                min={1}
                                max={stock}
                                type="number"
                            />
                            <IconButton
                                aria-label="Increase quantity"
                                icon={<FaPlus />}
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuantityChange(quantity + 1)}
                                disabled={quantity >= stock}
                            />
                        </HStack>
                    </Box>
                )}

                {/* Total Price */}
                {quantity > 1 && (
                    <Box>
                        <Text fontSize="sm" color="gray.600">
                            Total: <Text as="span" fontWeight="bold" color="green.600">{formatUSD(totalPrice)}</Text>
                        </Text>
                    </Box>
                )}

                {/* Action Buttons */}
                <Stack gap={3}>
                    {stock > 0 ? (
                        <>
                            {/* Buy Now Button */}
                            <Button
                                colorScheme="blue"
                                size="lg"
                                onClick={handleBuyNow}
                                loading={isBuying}
                                disabled={isBuying || isAddingToCart}
                            >
                                Buy Now - {formatUSD(totalPrice)}
                            </Button>

                            {/* Add to Cart Button */}
                            <Button
                                variant="outline"
                                colorScheme="blue"
                                size="lg"
                                leftIcon={<FaShoppingCart />}
                                onClick={handleAddToCart}
                                loading={isAddingToCart}
                                disabled={isAddingToCart || isBuying}
                            >
                                {isInCart ? 'Update Cart' : 'Add to Cart'}
                            </Button>
                        </>
                    ) : (
                        <Button
                            size="lg"
                            disabled
                            colorScheme="gray"
                        >
                            Out of Stock
                        </Button>
                    )}

                    {/* Wishlist Button */}
                    <Button
                        variant="ghost"
                        size="sm"
                        leftIcon={<FaHeart />}
                        onClick={handleAddToWishlist}
                        color={isInWishlist ? "red.500" : "gray.600"}
                        _hover={{
                            color: isInWishlist ? "red.600" : "red.500",
                            bg: isInWishlist ? "red.50" : "gray.50"
                        }}
                    >
                        {isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}
                    </Button>
                </Stack>

                {/* Product Info */}
                <Box pt={4} borderTop="1px solid" borderColor="gray.100">
                    <Text fontSize="xs" color="gray.500">
                        • Free shipping on orders over $50
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • 30-day return policy
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                        • Secure payment processing
                    </Text>
                </Box>
            </VStack>
        </Box>
    );
};

export default BuyNowInfo;
