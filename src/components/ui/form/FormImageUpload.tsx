"use client"

import {
    Box,
    Button,
    Field,
    Flex,
    Grid,
    Icon,
    Image,
    Text,
    Stack,
    IconButton,
    HStack
} from '@chakra-ui/react'
import React, { useCallback, useState } from 'react'
import { FaCamera, FaTrash } from 'react-icons/fa'
import { useDropzone } from 'react-dropzone'

interface ImageFile {
    id: string
    file: File
    preview: string
    isMain?: boolean
}

interface FormImageUploadProps {
    label?: string
    description?: string
    required?: boolean
    errorText?: string
    maxFiles?: number
    maxSizeInMB?: number
    acceptedFileTypes?: string[]
    onImagesChange?: (images: ImageFile[]) => void
    value?: ImageFile[]
}

const FormImageUpload: React.FC<FormImageUploadProps> = ({
    label = "Product Images",
    description = "Upload multiple images of your product. First image will be the main image.",
    required = false,
    errorText,
    maxFiles = 10,
    maxSizeInMB = 5,
    acceptedFileTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    onImagesChange,
    value = []
}) => {
    const [images, setImages] = useState<ImageFile[]>(value)

    const onDrop = useCallback((acceptedFiles: File[]) => {
        const newImages: ImageFile[] = acceptedFiles.map((file, index) => ({
            id: `${Date.now()}-${index}`,
            file,
            preview: URL.createObjectURL(file),
            isMain: images.length === 0 && index === 0
        }))

        const updatedImages = [...images, ...newImages].slice(0, maxFiles)
        setImages(updatedImages)
        onImagesChange?.(updatedImages)
    }, [images, maxFiles, onImagesChange])

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: acceptedFileTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
        maxSize: maxSizeInMB * 1024 * 1024,
        multiple: true
    })

    const removeImage = (id: string) => {
        const updatedImages = images.filter(img => img.id !== id)
        // If we removed the main image, make the first remaining image the main one
        if (updatedImages.length > 0 && !updatedImages.some(img => img.isMain)) {
            updatedImages[0].isMain = true
        }
        setImages(updatedImages)
        onImagesChange?.(updatedImages)
    }

    const setMainImage = (id: string) => {
        const updatedImages = images.map(img => ({
            ...img,
            isMain: img.id === id
        }))
        setImages(updatedImages)
        onImagesChange?.(updatedImages)
    }

    return (
        <Field.Root required={required}>
            <Field.Label fontWeight="bold" color="gray.600">
                {label}
                {required && <Field.RequiredIndicator>*</Field.RequiredIndicator>}
            </Field.Label>
            {description && (
                <Field.Label color="gray.500" fontSize="sm" mb={3}>
                    {description}
                </Field.Label>
            )}

            {/* Upload Area */}
            <Box
                {...getRootProps()}
                border="2px dashed"
                borderColor={isDragActive ? "blue.400" : "gray.300"}
                borderRadius="md"
                p={6}
                textAlign="center"
                cursor="pointer"
                bg={isDragActive ? "blue.50" : "gray.50"}
                transition="all 0.2s"
                _hover={{ borderColor: "blue.400", bg: "blue.50" }}
                mb={4}
            >
                <input {...getInputProps()} />
                <Stack gap={3} align="center">
                    <Icon as={FaCamera} boxSize={8} color="gray.400" />
                    <Text color="gray.600" fontWeight="medium">
                        {isDragActive ? "Drop images here..." : "Click or drag images here"}
                    </Text>
                    <Text fontSize="sm" color="gray.500">
                        Max {maxFiles} images, up to {maxSizeInMB}MB each
                    </Text>
                    <Text fontSize="xs" color="gray.400">
                        Supported: JPG, PNG, WebP
                    </Text>
                </Stack>
            </Box>

            {/* Image Preview Grid */}
            {images.length > 0 && (
                <Grid templateColumns="repeat(auto-fill, minmax(120px, 1fr))" gap={3} mb={4}>
                    {images.map((image, index) => (
                        <Box key={image.id} position="relative" borderRadius="md" overflow="hidden">
                            <Image
                                src={image.preview}
                                alt={`Product image ${index + 1}`}
                                w="100%"
                                h="120px"
                                objectFit="cover"
                            />

                            {/* Main Image Badge */}
                            {image.isMain && (
                                <Box
                                    position="absolute"
                                    top={2}
                                    left={2}
                                    bg="blue.500"
                                    color="white"
                                    px={2}
                                    py={1}
                                    borderRadius="sm"
                                    fontSize="xs"
                                    fontWeight="bold"
                                >
                                    MAIN
                                </Box>
                            )}

                            {/* Remove Button */}
                            <IconButton
                                position="absolute"
                                top={2}
                                right={2}
                                aria-label="Remove image"
                                size="xs"
                                colorScheme="red"
                                onClick={() => removeImage(image.id)}
                            >
                                <FaTrash />
                            </IconButton>

                            {/* Set as Main Button */}
                            {!image.isMain && (
                                <Button
                                    position="absolute"
                                    bottom={2}
                                    left={2}
                                    right={2}
                                    size="xs"
                                    colorScheme="blue"
                                    variant="solid"
                                    onClick={() => setMainImage(image.id)}
                                >
                                    Set as Main
                                </Button>
                            )}
                        </Box>
                    ))}
                </Grid>
            )}

            {errorText && <Field.ErrorText>{errorText}</Field.ErrorText>}
        </Field.Root>
    )
}

export default FormImageUpload
