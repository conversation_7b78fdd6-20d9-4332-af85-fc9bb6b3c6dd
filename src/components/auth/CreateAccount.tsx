import { <PERSON>, Button, Field, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, In<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@chakra-ui/react"
import Link from "next/link"
import { FaGoogle } from "react-icons/fa"
import { useTranslations } from "next-intl"
import { withMask } from "use-mask-input"
import FormIn<PERSON>Field from "../ui/form/FormInputField"
import { Controller, useForm } from "react-hook-form"
import { MutationRegister } from "@/services/useAuthQuery"
import { useState } from "react"
import { signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { toaster } from "../ui/toaster"
import { useAppStore } from "@/stores/app/store"
import GoogleSignInButton from "./GoogleSignInButton"

type FormRegisterValues = {
    firstName: string
    lastName: string
    email: string
    phoneNumber: string
    password: string
    confirmPassword: string
}

const CreateAccount: React.FC = () => {
    const t = useTranslations()
    const router = useRouter()
    const { isLoading, setLoading } = useAppStore()
    const [errorInput, setErrorInput] = useState<FormRegisterValues>({
        firstName: '',
        lastName: '',
        email: '',
        phoneNumber: '',
        password: '',
        confirmPassword: ''
    });

    const {
        register,
        handleSubmit,
        formState: { errors },
        watch,
        control,
    } = useForm<FormRegisterValues>({
        defaultValues: {
            firstName: '',
            lastName: '',
            email: '',
            phoneNumber: '',
            password: '',
            confirmPassword: '',
        },
    });

    const { mutate: registerAuth } = MutationRegister({
        onError: (error: any) => {
            const errorsData = error.response?.data?.error;
            if (!errorsData) return;
            setErrorInput(errorsData);
        },
        onSuccess: async (data) => {
            setLoading(true);

            const result = await signIn("credentials", {
                email: watch("email"),
                password: watch("password"),
                redirect: false
            })

            if (result?.ok) {
                toaster.create({
                    title: "Registration Successful",
                    type: "success",
                })
                router.push('/');
            }

            setLoading(false);

        },
    });

    const onSubmit = (data: FormRegisterValues) => {
        registerAuth(data)
    }

    return (
        <>
            <Heading
                fontSize={'xl'}
                color={'gray.800'}>
                Create an account
            </Heading>

            <form onSubmit={handleSubmit(onSubmit)}>
                <Stack gap={4} mt={4}>
                    <HStack gap={4} width="full" alignItems="start">
                        <FormInputField
                            label="First Name"
                            required
                            {...register("firstName", {
                                required: "First name is required",
                            })}
                            errorText={errors.firstName?.message || errorInput.firstName?.[0]}
                            invalid={!!errors.firstName || !!errorInput.firstName?.[0]}
                        />
                        <FormInputField
                            label="Last Name"
                            required
                            {...register("lastName", {
                                required: "Last name is required",
                            })}
                            errorText={errors.lastName?.message || errorInput.lastName?.[0]}
                            invalid={!!errors.lastName || !!errorInput.lastName?.[0]}
                        />
                    </HStack>
                    <FormInputField
                        label="Email"
                        required
                        type="email"
                        placeholder=""
                        {...register("email", {
                            required: "Email is required",
                            pattern: {
                                value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                                message: "Invalid email address"
                            }
                        })}
                        errorText={errors.email?.message || errorInput.email?.[0]}
                        invalid={!!errors.email || !!errorInput.email?.[0]}
                    />
                    <Controller
                        name="phoneNumber"
                        control={control}
                        rules={{
                            required: "Phone number is required",
                            pattern: {
                                value: /^\(\d{2}\) \d{9,11}$/,
                                message: "Invalid phone number format",
                            },
                        }}
                        render={({ field, fieldState }) => (
                            <Field.Root required invalid={!!fieldState.error || !!errorInput.phoneNumber?.[0]}>
                                <Field.Label fontWeight="bold" color="gray.800">
                                    Phone Number
                                    <Field.RequiredIndicator />
                                </Field.Label>
                                <Input
                                    placeholder="(62) 812345678"
                                    {...field}
                                    ref={(el) => {
                                        field.ref(el)
                                        if (el) withMask("(99) 99999999999")(el)
                                    }}
                                />
                                <Field.ErrorText>{fieldState.error?.message || errorInput.phoneNumber?.[0]}</Field.ErrorText>
                            </Field.Root>
                        )}
                    />


                    <FormInputField
                        label="Password"
                        type="password"
                        required
                        {...register("password", {
                            required: "Password is required",
                            minLength: {
                                value: 8,
                                message: "Password must be at least 8 characters long"
                            }
                        })}
                        errorText={errors.password?.message || errorInput.password?.[0]}
                        invalid={!!errors.password || !!errorInput.password?.[0]}
                    />
                    <FormInputField
                        label="Confirm Password"
                        type="password"
                        required
                        {...register("confirmPassword", {
                            required: "Confirm password is required",
                            validate: (value) => {
                                const password = watch("password");
                                return value === password || "Passwords do not match";
                            }
                        })}
                        errorText={errors.confirmPassword?.message || errorInput.confirmPassword?.[0]}
                        invalid={!!errors.confirmPassword || !!errorInput.confirmPassword?.[0]}
                    />
                    <Button
                        type="submit"
                        borderRadius="lg"
                        variant={'solid'}
                        w="full"
                        disabled={isLoading}
                        mt={2}>
                        {isLoading ? <Spinner /> : t("Button.register")}
                    </Button>
                </Stack>
            </form>

            <HStack w="100%" align="center" my={4}>
                <Box flex="1" h="1px" bg="gray.200" />
                <Text px={2} color="gray.500" fontSize="sm">
                    or continue with
                </Text>
                <Box flex="1" h="1px" bg="gray.200" />
            </HStack>

            <GoogleSignInButton
                onSuccess={() => {
                    toaster.create({
                        title: "Google Sign In Successful",
                        description: "Welcome! Redirecting to dashboard...",
                        type: "success",
                    });
                }}
                onError={(error) => {
                    toaster.create({
                        title: "Google Sign In Failed",
                        description: error.message,
                        type: "error",
                    });
                }}
            />

            <HStack justifyContent="center" mt={4}>
                <Text fontSize="sm" color="gray.600">
                    Already have an account?{" "}
                </Text>
                <Link
                    href="/auth/login"
                >
                    <Text
                        color="gray.700"
                        fontSize="sm"
                        fontWeight="semibold"
                        textDecoration="underline"
                        _hover={{ color: 'blue.700' }}
                    >
                        {t('Button.login')}
                    </Text>
                </Link>
            </HStack>
        </>
    )
}

export default CreateAccount