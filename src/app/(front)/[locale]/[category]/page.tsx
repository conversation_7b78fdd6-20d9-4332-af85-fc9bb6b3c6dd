'use client';

import ProductList from '@/components/product/ProductList';
import ProductFilter from '@/components/product/ProductFilter';
import { ProductQueryParams } from '@/services/useProductQuery';
import {
    Box,
    Grid,
    GridItem,
    Heading,
    HStack,
    Text,
    Container
} from '@chakra-ui/react';
import { useState } from 'react';
import { MenuList } from '@/components/layouts/front/navbar/Navbar';
import { notFound, useParams, usePathname } from 'next/navigation';

const AllProductsPage = () => {
    const pathname = usePathname();
    const params = useParams();
    const { locale } = params;
    const [filters, setFilters] = useState<Partial<ProductQueryParams>>({
        status: 'active'
    });

    const cleanedPath = pathname.replace(`/${locale}`, '') || '/';
    const menuExists = MenuList.some(menu => {
        const menuHref = menu.href.split('?')[0].split('#')[0];
        return menuHref === cleanedPath;
    });
    if (!menuExists) {
        if (typeof window !== "undefined") {
            return notFound();
        }
        return null;
    }

    const handleFiltersChange = (newFilters: Partial<ProductQueryParams>) => {
        setFilters(newFilters);
    };

    const handleClearFilters = () => {
        setFilters({ status: 'active' });
    };

    return (
        <Container maxW="7xl" py={8}>
            <Box mb={6}>
                <Heading as="h1" size="xl" mb={2}>
                    All Products
                </Heading>
                <Text color="gray.600">
                    Discover amazing collectibles from our community
                </Text>
            </Box>

            <Grid templateColumns={{ base: '1fr', lg: '300px 1fr' }} gap={8}>
                {/* Filter Sidebar */}
                <GridItem>
                    <Box position="sticky" top="100px">
                        <ProductFilter
                            filters={filters}
                            onFiltersChange={handleFiltersChange}
                            onClearFilters={handleClearFilters}
                        />
                    </Box>
                </GridItem>

                {/* Products List */}
                <GridItem>
                    <ProductList
                        filters={filters}
                        showPagination={true}
                        limit={12}
                    />
                </GridItem>
            </Grid>
        </Container>
    );
};

export default AllProductsPage;