'use client'
import { Box, Heading, Text, VStack, Skeleton, Stack, Badge } from '@chakra-ui/react';
import { useParams } from 'next/navigation';
import ProductImageGallery from '@/components/product/ProductImageGallery';
import ProductDetailLayout from '@/components/product/ProductDetailLayout';
import AuctionInfo from '@/components/product/AuctionInfo';
import BuyNowInfo from '@/components/product/BuyNowInfo';
import SalesHistory from '@/components/product/SalesHistory';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { useProductBySlugQuery } from '@/services/useProductQuery';
import { formatDistanceToNow } from 'date-fns';
import ProductCategory from '@/components/product/ProductCategory';
import products from '@/datas/product.json';
import ProductInfo from '@/components/product/ProductInfo';
import { formatUSD } from '@/utils/helpers/helper';
import {
  useAddToCartMutation,
  useAddToWishlistMutation,
  useRemoveFromWishlistMutation,
  useCartQuery,
  useWishlistQuery
} from '@/services/useCartQuery';
import { useBuyNowMutation } from '@/services/useCheckoutQuery';
import { usePlaceBidMutation } from '@/services/useBiddingQuery';
import { toaster } from '@/components/ui/toaster';

const ProductDetailPage = () => {
    const { locale, category, slug } = useParams();

    const {
        data: product,
        isLoading,
        error,
        isError
    } = useProductBySlugQuery(slug as string);

    // Loading state
    if (isLoading) {
        return (
            <Box>
                <ProductDetailLayout
                    leftContent={
                        <VStack gap={4} align="stretch">
                            <Box px={{ base: 0, md: 6 }}>
                                <Skeleton height="20px" width="300px" />
                            </Box>
                            <Box px={{ base: 0, md: 6 }}>
                                <Skeleton height={{ base: '300px', lg: '500px', xl: '650px' }} />
                            </Box>
                        </VStack>
                    }
                    rightContent={
                        <Stack gap={6}>
                            <Skeleton height="40px" />
                            <Skeleton height="20px" />
                            <Skeleton height="100px" />
                            <Skeleton height="200px" />
                        </Stack>
                    }
                />
            </Box>
        );
    }

    // Error state
    if (isError || !product) {
        return (
            <Box textAlign="center" py={20}>
                <Text fontSize="xl" color="red.500" mb={4}>
                    {error instanceof Error ? error.message : 'Product not found'}
                </Text>
                <Text color="gray.600">
                    The product you're looking for doesn't exist or has been removed.
                </Text>
            </Box>
        );
    }

    // Breadcrumb items
    const breadcrumbItems = [
        {
            label: 'Home',
            href: '/'
        },
        {
            label: 'Auction',
            href: '/auction'
        },
        {
            label: product.itemName,
            isCurrentPage: true
        }
    ];

    // Calculate time left for auction
    const timeLeft = product.auctionEndDate
        ? formatDistanceToNow(new Date(product.auctionEndDate), { addSuffix: true })
        : null;

    // Query hooks
    const { data: cart } = useCartQuery();
    const { data: wishlist } = useWishlistQuery();
    const addToCartMutation = useAddToCartMutation();
    const addToWishlistMutation = useAddToWishlistMutation();
    const removeFromWishlistMutation = useRemoveFromWishlistMutation();
    const buyNowMutation = useBuyNowMutation();

    // Check if product is in cart/wishlist
    const isInCart = cart?.items.some(item => item.productId === product.id) || false;
    const isInWishlist = wishlist?.items.some(item => item.productId === product.id) || false;

    // Handle bid placement - now handled by AuctionInfo component directly
    const handlePlaceBid = async (productId: string, bidAmount: number) => {
        console.log('Place bid for product:', productId, 'Amount:', bidAmount);
        // This is now handled by the AuctionInfo component using usePlaceBidMutation
    };

    // Handle bid history
    const handleShowBidHistory = (productId: string) => {
        console.log('Show bid history for product:', productId);
        // TODO: Open bid history modal or navigate to bid history page
        toaster.create({
            title: "Bid History",
            description: "Bid history feature coming soon!",
            type: "info",
        });
    };

    // Handle add to cart
    const handleAddToCart = async (productId: string, quantity: number) => {
        try {
            await addToCartMutation.mutateAsync({ productId, quantity });
        } catch (error) {
            console.error('Failed to add to cart:', error);
        }
    };

    // Handle buy now
    const handleBuyNow = async (productId: string, quantity: number) => {
        try {
            // For now, just show a message. In real implementation, this would redirect to checkout
            toaster.create({
                title: "Redirecting to Checkout",
                description: "You will be redirected to complete your purchase.",
                type: "info",
            });
            console.log('Buy now for product:', productId, 'Quantity:', quantity);
            // TODO: Redirect to checkout page with product data
        } catch (error) {
            console.error('Failed to buy now:', error);
        }
    };

    // Handle wishlist toggle
    const handleToggleWishlist = async (productId: string) => {
        try {
            if (isInWishlist) {
                await removeFromWishlistMutation.mutateAsync(productId);
            } else {
                await addToWishlistMutation.mutateAsync(productId);
            }
        } catch (error) {
            console.error('Failed to toggle wishlist:', error);
        }
    };

    // Handle sales history
    const handleViewSalesHistory = () => {
        console.log('View sales history for product:', product.id);
        // TODO: Implement sales history view logic
    };

    // Transform API product to match ProductItem interface for existing components
    const transformedProduct = {
        id: product.id,
        slug: product.slug || '',
        title: product.itemName,
        image: product.images.find(img => img.isMain)?.imageUrl || product.images[0]?.imageUrl || '',
        images: product.images.map(img => img.imageUrl), // ProductItem expects string[] for images
        price: `$${product.priceUSD}`, // ProductItem expects string for price
        bids: product.bidCount?.toString() || '0', // ProductItem expects string for bids
        timeLeft: timeLeft || '',
    };

    console.log(transformedProduct)

    const leftContent = (
        <VStack gap={4} align="stretch">
            <Box px={{ base: 0, md: 6 }}>
                <Breadcrumb items={breadcrumbItems} />
            </Box>

            <Box
                position="sticky"
                top={{ base: 4, md: 8 }}
                px={{ base: 0, md: 6 }}
            >
                <ProductImageGallery
                    item={transformedProduct}
                    boxSizeWatchList={6}
                    containerProps={{
                        height: { base: 'full', lg: '500px', xl: '650px' },
                    }}
                />
            </Box>
        </VStack>
    );

    const rightContent = (
        <>
            <Box mb={6}>
                <Stack gap={3}>
                    <Badge
                        colorScheme={product.sellType === 'auction' ? 'blue' : 'green'}
                        variant="solid"
                        width="fit-content"
                        fontSize="sm"
                        px={3}
                        py={1}
                    >
                        {product.sellType === 'auction' ? 'Auction' : 'Buy Now'}
                    </Badge>
                    <ProductInfo
                        title={product.itemName}
                        subtitle={product.category?.name || '-'}
                    />
                </Stack>
            </Box>

            {/* Render different components based on sell type */}
            {product.sellType === 'auction' ? (
                <AuctionInfo
                    currentBid={product.currentBid || product.priceUSD}
                    startingPrice={product.priceUSD}
                    bidCount={product.bidCount ?? 0}
                    auctionStartDate={product.auctionStartDate || new Date().toISOString()}
                    auctionEndDate={product.auctionEndDate || new Date().toISOString()}
                    extendedBiddingEnabled={product.extendedBiddingEnabled}
                    extendedBiddingMinutes={product.extendedBiddingMinutes}
                    extendedBiddingDuration={product.extendedBiddingDuration}
                    productId={product.id}
                    productName={product.itemName}
                    onPlaceBid={handlePlaceBid}
                    onAddToWishlist={handleToggleWishlist}
                    onShowBidHistory={handleShowBidHistory}
                    isInWishlist={isInWishlist}
                    mb={6}
                />
            ) : (
                <BuyNowInfo
                    price={product.priceUSD}
                    stock={1} // Default stock for buy-now products
                    productId={product.id}
                    productName={product.itemName}
                    onAddToCart={handleAddToCart}
                    onBuyNow={handleBuyNow}
                    onAddToWishlist={handleToggleWishlist}
                    isInWishlist={isInWishlist}
                    isInCart={isInCart}
                    mb={6}
                />
            )}

            <Box as="hr" my={6} borderColor="gray.200" />

            <Box>
                <Heading as="h3" size="md" mb={3}>
                    Description
                </Heading>
                <Text fontSize="sm" mb={4}>
                    {product.description || 'No description available for this product.'}
                </Text>
            </Box>

            <Box as="hr" my={6} borderColor="gray.200" />

            <SalesHistory
                onLinkClick={handleViewSalesHistory}
            />
        </>
    );

    return (
        <Box>
            <Box>
                <ProductDetailLayout
                    leftContent={leftContent}
                    rightContent={rightContent}
                />
            </Box>

            <ProductCategory
                items={products}
                title="Related Items"
            />

            <ProductCategory
                items={products}
                title="Similar Items"
            />
        </Box>
    );
};

export default ProductDetailPage;