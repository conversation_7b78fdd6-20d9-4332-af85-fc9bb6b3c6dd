'use client'
import ProductCategory from "@/components/product/ProductCategory"
import ProductList from "@/components/product/ProductList"
import { ProductItem } from "@/types/product"
import { Box, Container, Heading, Stack } from "@chakra-ui/react"
import dynamic from "next/dynamic"
import React from 'react'
import items from '@/datas/product.json'

const HeroSlider = dynamic(() => import('@/components/slider/HeroSlider'))

export default function page() {
  const sliderImages = [
    '/assets/images/banner-1.png',
    '/assets/images/banner-2.png',
  ]

  return (
    <Box>
      <Box bg="white" py={4}>
        <HeroSlider images={sliderImages} />
      </Box>

      {/* Featured Products from API */}
      <Container maxW="7xl" py={8}>
        <Stack spacing={8}>
          <Box>
            <Heading as="h2" size="lg" mb={6} textAlign="center">
              Featured Products
            </Heading>
            <ProductList
              filters={{ status: 'active' }}
              limit={8}
              showPagination={false}
            />
          </Box>

          <Box>
            <Heading as="h2" size="lg" mb={6} textAlign="center">
              Latest Auctions
            </Heading>
            <ProductList
              filters={{
                status: 'active',
                sellType: 'auction',
                sortBy: 'auctionEndDate',
                sortOrder: 'asc'
              }}
              limit={8}
              showPagination={false}
            />
          </Box>
        </Stack>
      </Container>

      {/* Legacy Product Categories */}
      <ProductCategory
        items={items}
        title="Set For Stun: Star Wars Indonesia"
      />
      <ProductCategory
        items={items}
        title="Shining Stars: Limited Edition Star Wars Posters"
      />
    </Box>
  )
}