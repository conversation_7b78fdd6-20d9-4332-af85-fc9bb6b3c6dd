# Authentication System Setup

## Overview

This project implements a secure, modern authentication system with the following features:

- **NextAuth.js** for frontend authentication
- **JWT tokens** with refresh token mechanism
- **Google OAuth** integration
- **Profile API** for session management
- **Rate limiting** and security middleware
- **OWASP security compliance**

## Architecture

### Backend (Hono.js + Prisma)
- JWT-based authentication with access/refresh tokens
- Google OAuth token verification
- Rate limiting middleware
- Secure password hashing with bcrypt
- Profile API endpoint

### Frontend (Next.js + NextAuth)
- NextAuth.js for session management
- Custom auth hooks and utilities
- Protected route middleware
- Automatic token refresh
- Google OAuth integration

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure:

```bash
cp .env.example .env.local
```

Required variables:
- `DATABASE_URL`: MySQL database connection
- `NEXTAUTH_SECRET`: Secret for NextAuth.js
- `JWT_SECRET`: Secret for JWT tokens (different from NEXTAUTH_SECRET)
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `NEXT_PUBLIC_API_URL`: Backend API URL

### 2. Database Setup

Run Prisma migrations:

```bash
npx prisma migrate dev
npx prisma generate
```

### 3. Google OAuth Setup

#### Step-by-step Google Cloud Console Setup:

1. **Go to Google Cloud Console**
   - Visit [Google Cloud Console](https://console.cloud.google.com/)
   - Sign in with your Google account

2. **Create or Select Project**
   - Click "Select a project" dropdown
   - Create new project or select existing one
   - Note the project ID for reference

3. **Enable Required APIs**
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
   - Search for "People API" and enable it (for profile information)

4. **Configure OAuth Consent Screen**
   - Go to "APIs & Services" > "OAuth consent screen"
   - Choose "External" user type (unless using Google Workspace)
   - Fill required fields:
     - App name: "King Collectibles"
     - User support email: your email
     - Developer contact information: your email
   - Add scopes: `email`, `profile`, `openid`
   - Add test users if in testing mode

5. **Create OAuth 2.0 Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add authorized redirect URIs:
     ```
     http://localhost:3000/api/auth/callback/google
     https://yourdomain.com/api/auth/callback/google
     ```
   - Copy Client ID and Client Secret

6. **Update Environment Variables**
   ```bash
   GOOGLE_CLIENT_ID="your-google-client-id-here"
   GOOGLE_CLIENT_SECRET="your-google-client-secret-here"
   ```

## Usage

### Frontend Authentication

#### Using the Login Component
The `LoginAccount` component is already integrated with Google Auth:

```tsx
import LoginAccount from '@/components/auth/LoginAccount';

function LoginPage() {
  return <LoginAccount />;
}
```

#### Using Auth Hooks
```tsx
import { useAuth } from '@/hooks/useAuth';

function CustomLoginComponent() {
  const { login, loginWithGoogle, isLoading, error } = useAuth();

  const handleLogin = async (email: string, password: string) => {
    const result = await login(email, password);
    if (result.success) {
      // Redirect handled automatically
    }
  };

  const handleGoogleLogin = async () => {
    await loginWithGoogle();
    // Redirect handled automatically
  };

  return (
    <div>
      <button onClick={() => handleLogin('<EMAIL>', 'password')}>
        Login with Email
      </button>
      <button onClick={handleGoogleLogin}>
        Continue with Google
      </button>
    </div>
  );
}
```

### Protected Routes

```tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

function Dashboard() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}
```

### API Calls with Authentication

```tsx
import { authService } from '@/lib/auth-utils';

// Automatically handles token refresh
const profile = await authService.getProfile();
```

## Security Features

### 1. Token Management
- **Access tokens**: 15-minute expiry
- **Refresh tokens**: 7-day expiry
- **Automatic refresh**: Before token expiry
- **Secure storage**: HttpOnly cookies (recommended) or localStorage

### 2. Rate Limiting
- **Auth endpoints**: 50 requests per 15 minutes
- **IP-based tracking**: Prevents brute force attacks
- **Configurable limits**: Per endpoint customization

### 3. Security Headers
- **X-Frame-Options**: Prevents clickjacking
- **X-Content-Type-Options**: Prevents MIME sniffing
- **CSP**: Content Security Policy
- **XSS Protection**: Cross-site scripting prevention

### 4. Input Validation
- **Zod schemas**: Type-safe validation
- **Password requirements**: Minimum 8 characters
- **Email validation**: RFC compliant
- **Phone number validation**: International format

## API Endpoints

### Authentication
- `POST /auth/login` - Email/password login
- `POST /auth/register` - User registration
- `POST /auth/google` - Google OAuth login
- `POST /auth/refresh-token` - Token refresh
- `GET /auth/profile` - Get user profile (protected)

### Request/Response Examples

#### Login
```bash
POST /auth/login
{
  "emailPhoneNumber": "<EMAIL>",
  "password": "password123"
}

Response:
{
  "status": true,
  "message": "Login successful",
  "data": {
    "accessToken": "eyJ...",
    "refreshToken": "eyJ...",
    "user": {
      "id": "user-id",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phoneNumber": "+1234567890"
    },
    "expiresAt": 1640995200
  }
}
```

## Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Manual Testing

#### 1. Test Email/Password Login
1. Go to `http://localhost:3000/auth/login`
2. Enter valid email and password
3. Should redirect to `/panel` on success
4. Should show error message on failure

#### 2. Test Google OAuth
1. Go to `http://localhost:3000/auth/login`
2. Click "Continue with Google"
3. Should open Google OAuth popup
4. After authorization, should redirect to `/panel`
5. User profile should be created/updated in database

#### 3. Test Protected Routes
1. Try accessing `/panel` without login
2. Should redirect to `/auth/login`
3. After login, should redirect back to `/panel`

#### 4. Test Token Refresh
1. Login and wait for token to expire (15 minutes)
2. Make API call to protected endpoint
3. Should automatically refresh token
4. Should continue working without re-login

## Troubleshooting

### Common Issues

1. **Google OAuth not working**
   - Check redirect URIs in Google Console
   - Verify GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET
   - Ensure domain is authorized
   - Check OAuth consent screen configuration
   - Verify APIs are enabled (Google+ API, People API)

2. **Token refresh failing**
   - Check JWT_SECRET configuration
   - Verify refresh token in database
   - Check token expiry times
   - Ensure database schema is up to date

3. **Database connection issues**
   - Verify DATABASE_URL format
   - Check database server status
   - Run `npx prisma db push` to sync schema
   - Check if migration was applied correctly

4. **NextAuth session issues**
   - Verify NEXTAUTH_SECRET is set
   - Check NEXTAUTH_URL matches your domain
   - Clear browser cookies and localStorage
   - Check browser console for errors

### Debug Mode

Enable debug logging:

```bash
# NextAuth debug
DEBUG=nextauth* npm run dev

# Full debug
DEBUG=* npm run dev
```

### Checking Database

```bash
# View database in Prisma Studio
npx prisma studio

# Reset database (development only)
npx prisma migrate reset

# Generate Prisma client
npx prisma generate
```

## Production Deployment

### Security Checklist

- [ ] Use strong, unique secrets for JWT_SECRET and NEXTAUTH_SECRET
- [ ] Enable HTTPS in production
- [ ] Configure proper CORS settings
- [ ] Set up Redis for rate limiting
- [ ] Enable database SSL
- [ ] Configure proper CSP headers
- [ ] Set up monitoring and logging
- [ ] Regular security updates

### Environment Variables

Ensure all production environment variables are set:
- Use secure random secrets
- Configure production database
- Set NEXTAUTH_URL to production domain
- Update Google OAuth redirect URIs

## Contributing

When contributing to the auth system:

1. Follow security best practices
2. Add tests for new features
3. Update documentation
4. Review security implications
5. Test with different browsers and devices
