import { PrismaClient } from '../generated/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create categories
  const categories = [
    {
      name: 'Sport',
      description: 'Sports trading cards and memorabilia'
    },
    {
      name: 'Non Sport',
      description: 'Non-sports trading cards and collectibles'
    },
    {
      name: 'Collectible',
      description: 'General collectible items'
    },
    {
      name: 'Gaming',
      description: 'Gaming cards and collectibles'
    },
    {
      name: 'Entertainment',
      description: 'Movies, TV shows, and entertainment collectibles'
    }
  ]

  console.log('📦 Creating categories...')
  const createdCategories = []
  for (const category of categories) {
    const created = await prisma.category.upsert({
      where: { name: category.name },
      update: {},
      create: category
    })
    createdCategories.push(created)
    console.log(`✅ Created category: ${created.name}`)
  }

  // Create item types
  const itemTypes = [
    // Sport category
    {
      name: 'Baseball Cards',
      description: 'Baseball trading cards',
      categoryName: 'Sport'
    },
    {
      name: 'Basketball Cards',
      description: 'Basketball trading cards',
      categoryName: 'Sport'
    },
    {
      name: 'Football Cards',
      description: 'Football trading cards',
      categoryName: 'Sport'
    },
    {
      name: 'Soccer Cards',
      description: 'Soccer trading cards',
      categoryName: 'Sport'
    },
    {
      name: 'Sports Memorabilia',
      description: 'Sports memorabilia and autographs',
      categoryName: 'Sport'
    },
    
    // Non Sport category
    {
      name: 'Star Wars',
      description: 'Star Wars trading cards and collectibles',
      categoryName: 'Non Sport'
    },
    {
      name: 'Marvel',
      description: 'Marvel trading cards and collectibles',
      categoryName: 'Non Sport'
    },
    {
      name: 'DC Comics',
      description: 'DC Comics trading cards and collectibles',
      categoryName: 'Non Sport'
    },
    {
      name: 'Disney',
      description: 'Disney trading cards and collectibles',
      categoryName: 'Non Sport'
    },
    
    // Gaming category
    {
      name: 'Pokemon',
      description: 'Pokemon trading cards',
      categoryName: 'Gaming'
    },
    {
      name: 'Magic: The Gathering',
      description: 'Magic: The Gathering cards',
      categoryName: 'Gaming'
    },
    {
      name: 'Yu-Gi-Oh!',
      description: 'Yu-Gi-Oh! trading cards',
      categoryName: 'Gaming'
    },
    {
      name: 'Video Game Collectibles',
      description: 'Video game related collectibles',
      categoryName: 'Gaming'
    },
    
    // Entertainment category
    {
      name: 'Movie Memorabilia',
      description: 'Movie related memorabilia',
      categoryName: 'Entertainment'
    },
    {
      name: 'TV Show Collectibles',
      description: 'TV show related collectibles',
      categoryName: 'Entertainment'
    },
    {
      name: 'Music Memorabilia',
      description: 'Music related memorabilia',
      categoryName: 'Entertainment'
    },
    
    // Collectible category
    {
      name: 'Vintage Items',
      description: 'Vintage collectible items',
      categoryName: 'Collectible'
    },
    {
      name: 'Autographs',
      description: 'Autographed items',
      categoryName: 'Collectible'
    },
    {
      name: 'Coins',
      description: 'Collectible coins',
      categoryName: 'Collectible'
    },
    {
      name: 'Stamps',
      description: 'Collectible stamps',
      categoryName: 'Collectible'
    }
  ]

  console.log('🏷️ Creating item types...')
  for (const itemType of itemTypes) {
    const category = createdCategories.find(c => c.name === itemType.categoryName)
    if (category) {
      const created = await prisma.itemType.upsert({
        where: { name: itemType.name },
        update: {},
        create: {
          name: itemType.name,
          description: itemType.description,
          categoryId: category.id
        }
      })
      console.log(`✅ Created item type: ${created.name}`)
    }
  }

  console.log('🎉 Database seeding completed!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
