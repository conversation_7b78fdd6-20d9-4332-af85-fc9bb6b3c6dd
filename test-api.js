// Simple API test script
const BASE_URL = 'http://localhost:3000/api/v1';

async function testAPI() {
  console.log('🧪 Testing Product API endpoints...\n');

  try {
    // Test 1: Get Categories
    console.log('1. Testing GET /products/categories');
    const categoriesResponse = await fetch(`${BASE_URL}/products/categories`);
    const categoriesResult = await categoriesResponse.json();
    console.log('Status:', categoriesResponse.status);
    console.log('Response:', JSON.stringify(categoriesResult, null, 2));
    console.log('✅ Categories endpoint working\n');

    // Test 2: Get Item Types
    console.log('2. Testing GET /products/item-types');
    const itemTypesResponse = await fetch(`${BASE_URL}/products/item-types`);
    const itemTypesResult = await itemTypesResponse.json();
    console.log('Status:', itemTypesResponse.status);
    console.log('Response:', JSON.stringify(itemTypesResult, null, 2));
    console.log('✅ Item types endpoint working\n');

    // Test 3: Get Products
    console.log('3. Testing GET /products');
    const productsResponse = await fetch(`${BASE_URL}/products`);
    const productsResult = await productsResponse.json();
    console.log('Status:', productsResponse.status);
    console.log('Response:', JSON.stringify(productsResult, null, 2));
    console.log('✅ Products endpoint working\n');

    console.log('🎉 All API endpoints are working correctly!');

  } catch (error) {
    console.error('❌ Error testing API:', error);
  }
}

// Run the test
testAPI();
